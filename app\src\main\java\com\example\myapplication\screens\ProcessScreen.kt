@file:OptIn(ExperimentalMaterial3Api::class)

package com.example.myapplication.screens

import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.myapplication.api.ApiService
import com.example.myapplication.api.RetrofitClient
import com.example.myapplication.model.EmployeeData
import com.example.myapplication.model.ProcessRequest
import com.example.myapplication.model.ProcessResponse
import kotlinx.coroutines.launch
import retrofit2.Response

@Composable
fun ProcessScreen(
    employeeData: EmployeeData,
    onServerConfigClick: () -> Unit,
    onBack: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    val scrollState = rememberScrollState()

    // 操作反馈状态
    var showFeedback by remember { mutableStateOf(false) }
    var feedbackMessage by remember { mutableStateOf("") }
    var isSuccess by remember { mutableStateOf(true) }

    // 显示加载中状态
    var isLoading by remember { mutableStateOf(false) }

    // 最后处理的流程类型
    var lastProcessType by remember { mutableStateOf("") }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text("批次处理")
                },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回扫描")
                    }
                },
                actions = {
                    IconButton(onClick = onServerConfigClick) {
                        Icon(Icons.Default.Settings, contentDescription = "设置")
                    }
                }
            )
        }
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .padding(16.dp)
                .verticalScroll(scrollState),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 流程选择部分
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "选择操作流程",
                        style = MaterialTheme.typography.titleLarge
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // 流程按钮
                    if (employeeData.serverUrl.isBlank()) {
                        Text(
                            text = "请先配置服务器地址",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.error
                        )
                    } else if (employeeData.batchId.isNotBlank()) {
                        // 流程按钮部分
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            // 入库按钮
                            ProcessButton(
                                label = "入库",
                                processType = "storage",
                                isActive = !isLoading && lastProcessType != "storage",
                                onClick = {
                                    coroutineScope.launch {
                                        submitProcess("storage", employeeData) { success, message ->
                                            showFeedback = true
                                            isSuccess = success
                                            feedbackMessage = message
                                            isLoading = false
                                            if (success) lastProcessType = "storage"
                                        }
                                        isLoading = true
                                    }
                                }
                            )

                            // 贴膜按钮
                            ProcessButton(
                                label = "贴膜",
                                processType = "film",
                                isActive = !isLoading && lastProcessType != "film",
                                onClick = {
                                    coroutineScope.launch {
                                        submitProcess("film", employeeData) { success, message ->
                                            showFeedback = true
                                            isSuccess = success
                                            feedbackMessage = message
                                            isLoading = false
                                            if (success) lastProcessType = "film"
                                        }
                                        isLoading = true
                                    }
                                }
                            )

                            // 切割按钮
                            ProcessButton(
                                label = "切割",
                                processType = "cutting",
                                isActive = !isLoading && lastProcessType != "cutting",
                                onClick = {
                                    coroutineScope.launch {
                                        submitProcess("cutting", employeeData) { success, message ->
                                            showFeedback = true
                                            isSuccess = success
                                            feedbackMessage = message
                                            isLoading = false
                                            if (success) lastProcessType = "cutting"
                                        }
                                        isLoading = true
                                    }
                                }
                            )

                            // 检验按钮
                            ProcessButton(
                                label = "检验",
                                processType = "inspection",
                                isActive = !isLoading && lastProcessType != "inspection",
                                onClick = {
                                    coroutineScope.launch {
                                        submitProcess("inspection", employeeData) { success, message ->
                                            showFeedback = true
                                            isSuccess = success
                                            feedbackMessage = message
                                            isLoading = false
                                            if (success) lastProcessType = "inspection"
                                        }
                                        isLoading = true
                                    }
                                }
                            )

                            // 出货按钮
                            ProcessButton(
                                label = "出货",
                                processType = "shipping",
                                isActive = !isLoading && lastProcessType != "shipping",
                                onClick = {
                                    coroutineScope.launch {
                                        submitProcess("shipping", employeeData) { success, message ->
                                            showFeedback = true
                                            isSuccess = success
                                            feedbackMessage = message
                                            isLoading = false
                                            if (success) lastProcessType = "shipping"
                                        }
                                        isLoading = true
                                    }
                                }
                            )
                        }
                    }
                }
            }

            // 加载中指示器
            if (isLoading) {
                Spacer(modifier = Modifier.height(24.dp))
                CircularProgressIndicator()
            }

            // 操作反馈
            if (showFeedback) {
                Spacer(modifier = Modifier.height(24.dp))

                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = if (isSuccess) MaterialTheme.colorScheme.primaryContainer else MaterialTheme.colorScheme.errorContainer,
                    shape = MaterialTheme.shapes.medium
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = if (isSuccess) "操作成功" else "操作失败",
                            style = MaterialTheme.typography.titleMedium,
                            color = if (isSuccess) MaterialTheme.colorScheme.onPrimaryContainer else MaterialTheme.colorScheme.onErrorContainer
                        )

                        Spacer(modifier = Modifier.height(4.dp))

                        Text(
                            text = feedbackMessage,
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (isSuccess) MaterialTheme.colorScheme.onPrimaryContainer else MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                TextButton(
                    onClick = { showFeedback = false }
                ) {
                    Text("关闭")
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 扫描新批次按钮
            OutlinedButton(
                onClick = onBack,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("返回扫描新批次")
            }
        }
    }
}

@Composable
fun ProcessButton(
    label: String,
    processType: String,
    isActive: Boolean,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        enabled = isActive
    ) {
        Text(label)
    }
}

// 提交流程数据到服务器
private suspend fun submitProcess(
    processType: String,
    employeeData: EmployeeData,
    onComplete: (Boolean, String) -> Unit
) {
    try {
        // 检查参数
        if (employeeData.serverUrl.isBlank()) {
            onComplete(false, "服务器地址未配置")
            return
        }

        if (employeeData.batchId.isBlank()) {
            onComplete(false, "批次ID为空")
            return
        }

        // 创建请求对象
        // 使用原始二维码内容作为qrContent
        val qrContent = employeeData.batchId

        // 记录请求详情，便于调试
        Log.d("ProcessScreen", "准备发送请求: 员工=${employeeData.employeeName}, 流程类型=$processType")
        Log.d("ProcessScreen", "原始二维码内容: $qrContent")
        Log.d("ProcessScreen", "客户代码: ${employeeData.customerCode}, 解析后的批次ID: ${employeeData.parsedBatchId}")

        val request = ProcessRequest(
            employee = employeeData.employeeName,
            company = employeeData.customerCode, // 使用解析后的客户代码作为company字段
            batchId = employeeData.parsedBatchId, // 使用解析后的批次ID
            processType = processType,
            qrContent = qrContent // 仍然发送完整的二维码内容，以便后端可以进行进一步处理
        )

        // 创建API服务
        val retrofit = RetrofitClient.getClient(employeeData.serverUrl)
        val apiService = retrofit.create(ApiService::class.java)

        // 发送请求
        try {
            Log.d("ProcessScreen", "开始发送API请求...")
            val response: Response<ProcessResponse> = apiService.recordProcess(request)

            // 记录响应详情
            Log.d("ProcessScreen", "收到响应: 状态码=${response.code()}")

            if (response.isSuccessful) {
                val body = response.body()
                if (body != null) {
                    Log.d("ProcessScreen", "响应内容: success=${body.success}, message=${body.message}")
                    if (body.success) {
                        onComplete(true, "流程记录成功")
                    } else {
                        onComplete(false, body.message ?: "服务器返回失败状态")
                    }
                } else {
                    Log.e("ProcessScreen", "响应体为空")
                    onComplete(false, "服务器响应内容为空")
                }
            } else {
                // 尝试获取错误响应体
                val errorBody = response.errorBody()?.string()
                Log.e("ProcessScreen", "请求失败: 状态码=${response.code()}, 错误信息=${response.message()}")
                Log.e("ProcessScreen", "错误响应体: $errorBody")
                onComplete(false, "服务器错误: ${response.code()} ${response.message()}\n${errorBody ?: ""}")
            }
        } catch (e: Exception) {
            Log.e("ProcessScreen", "API请求异常", e)
            onComplete(false, "网络错误: ${e.message}")
        }
    } catch (e: Exception) {
        Log.e("ProcessScreen", "提交流程数据失败", e)
        onComplete(false, "应用错误: ${e.message}")
    }
}