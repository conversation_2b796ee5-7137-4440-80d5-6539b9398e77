package com.example.myapplication.utils

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class DataStoreManager(private val context: Context) {
    companion object {
        private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "app_preferences")
        
        val EMPLOYEE_NAME = stringPreferencesKey("employee_name")
        val COMPANY_NAME = stringPreferencesKey("company_name")
        val SERVER_URL = stringPreferencesKey("server_url")
    }
    
    // 获取保存的员工姓名
    val employeeName: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[EMPLOYEE_NAME] ?: ""
        }
    
    // 获取保存的公司名称
    val companyName: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[COMPANY_NAME] ?: ""
        }
    
    // 获取保存的服务器URL
    val serverUrl: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[SERVER_URL] ?: ""
        }
    
    // 保存员工姓名
    suspend fun saveEmployeeName(name: String) {
        context.dataStore.edit { preferences ->
            preferences[EMPLOYEE_NAME] = name
        }
    }
    
    // 保存公司名称
    suspend fun saveCompanyName(company: String) {
        context.dataStore.edit { preferences ->
            preferences[COMPANY_NAME] = company
        }
    }
    
    // 保存服务器URL
    suspend fun saveServerUrl(url: String) {
        context.dataStore.edit { preferences ->
            preferences[SERVER_URL] = url
        }
    }
    
    // 清除所有保存的数据
    suspend fun clearAll() {
        context.dataStore.edit { it.clear() }
    }
} 