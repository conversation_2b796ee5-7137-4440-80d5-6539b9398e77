package com.example.myapplication

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.myapplication.model.EmployeeData
import com.example.myapplication.screens.ProcessScreen
import com.example.myapplication.screens.QRScannerScreen
import com.example.myapplication.screens.ServerConfigScreen
import com.example.myapplication.ui.theme.JiangsuXinggeTheme
import com.example.myapplication.utils.DataStoreManager
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        val dataStoreManager = DataStoreManager(this)

        setContent {
            JiangsuXinggeTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val navController = rememberNavController()
                    val scope = rememberCoroutineScope()

                    val employeeData = remember { mutableStateOf(EmployeeData()) }

                    // 从DataStore获取保存的数据
                    val savedEmployeeName by dataStoreManager.employeeName.collectAsState(initial = "")
                    val savedCompanyName by dataStoreManager.companyName.collectAsState(initial = "")
                    val savedServerUrl by dataStoreManager.serverUrl.collectAsState(initial = "http://192.168.1.100:3000/") // 默认服务器URL

                    // 使用保存的数据初始化
                    employeeData.value = EmployeeData(
                        employeeName = savedEmployeeName,
                        companyName = savedCompanyName,
                        serverUrl = savedServerUrl
                    )

                    // 默认直接进入扫描页面，除非第一次使用
                    val startDestination = if (savedEmployeeName.isBlank() || savedServerUrl.isBlank()) {
                        "settings"
                    } else {
                        "scanner"
                    }

                    NavHost(navController = navController, startDestination = startDestination) {
                        // 扫描二维码界面 - 主界面
                        composable("scanner") {
                            QRScannerScreen(
                                employeeData = employeeData.value,
                                onQRCodeScanned = { content ->
                                    employeeData.value = employeeData.value.copy(qrContent = content)
                                },
                                onBatchIdScanned = { batchId ->
                                    // 解析二维码内容，提取客户代码和批次ID
                                    var customerCode = ""
                                    var parsedBatchId = batchId

                                    try {
                                        // 如果包含分隔符"|"，则尝试分离客户代码和批次ID
                                        if (batchId.contains("|")) {
                                            val parts = batchId.split("|", limit = 2)
                                            if (parts.size == 2) {
                                                customerCode = parts[0]
                                                parsedBatchId = parts[1]
                                            }
                                        }
                                    } catch (e: Exception) {
                                        // 解析失败时，使用原始内容作为批次ID
                                        customerCode = ""
                                        parsedBatchId = batchId
                                    }

                                    // 设置解析后的客户代码和批次ID
                                    employeeData.value = employeeData.value.copy(
                                        batchId = batchId,
                                        customerCode = customerCode,
                                        parsedBatchId = parsedBatchId
                                    )
                                    // 扫描完二维码后直接导航到处理界面
                                    navController.navigate("process")
                                },
                                onSettingsClick = {
                                    // 通过左上角按钮进入服务器设置
                                    navController.navigate("settings")
                                },
                                onUpdateEmployeeInfo = { employee, company ->
                                    // 快速更新员工信息，公司信息将从二维码中自动解析
                                    employeeData.value = employeeData.value.copy(
                                        employeeName = employee
                                    )

                                    scope.launch {
                                        dataStoreManager.saveEmployeeName(employee)
                                    }
                                }
                            )
                        }

                        // 服务器配置界面 - 通过左上角按钮访问
                        composable("settings") {
                            ServerConfigScreen(
                                employeeData = employeeData.value,
                                onSaveConfig = { url, employee, company ->
                                    // 保存所有配置数据
                                    employeeData.value = EmployeeData(
                                        serverUrl = url,
                                        employeeName = employee,
                                        companyName = company
                                    )

                                    // 保存数据到DataStore
                                    scope.launch {
                                        dataStoreManager.saveServerUrl(url)
                                        dataStoreManager.saveEmployeeName(employee)
                                        dataStoreManager.saveCompanyName(company)
                                    }

                                    // 配置完成后返回扫描界面
                                    navController.navigate("scanner") {
                                        popUpTo("settings") { inclusive = true }
                                    }
                                },
                                onBack = {
                                    // 返回扫描界面
                                    navController.navigate("scanner") {
                                        popUpTo("settings") { inclusive = true }
                                    }
                                }
                            )
                        }

                        // 流程处理界面
                        composable("process") {
                            ProcessScreen(
                                employeeData = employeeData.value,
                                onServerConfigClick = {
                                    navController.navigate("settings")
                                },
                                onBack = {
                                    // 清除批次ID后再返回扫描页面
                                    employeeData.value = employeeData.value.copy(batchId = "")
                                    navController.navigate("scanner") {
                                        popUpTo("scanner") { inclusive = true }
                                    }
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}