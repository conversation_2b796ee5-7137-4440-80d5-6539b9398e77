@file:OptIn(ExperimentalMaterial3Api::class)

package com.example.myapplication.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.clickable
import androidx.compose.ui.draw.rotate
import com.example.myapplication.model.EmployeeData

// 可折叠卡片组件
@Composable
fun ExpandableCard(
    title: String,
    expanded: <PERSON><PERSON><PERSON>,
    onExpandChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题栏，点击可以切换展开/收缩状态
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onExpandChange(!expanded) },
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium
                )

                // 动画旋转箭头
                val rotation by animateFloatAsState(
                    targetValue = if (expanded) 180f else 0f,
                    label = "arrow_rotation"
                )

                Icon(
                    imageVector = Icons.Default.KeyboardArrowDown,
                    contentDescription = if (expanded) "收起" else "展开",
                    modifier = Modifier.rotate(rotation)
                )
            }

            // 内容区域的动画显示/隐藏
            AnimatedVisibility(
                visible = expanded,
                enter = expandVertically(),
                exit = shrinkVertically()
            ) {
                Column {
                    Spacer(modifier = Modifier.height(16.dp))
                    content()
                }
            }
        }
    }
}

@Composable
fun ServerConfigScreen(
    employeeData: EmployeeData,
    onSaveConfig: (serverUrl: String, employeeName: String, companyName: String) -> Unit,
    onBack: () -> Unit
) {
    var serverUrl by remember { mutableStateOf(employeeData.serverUrl) }
    var employeeName by remember { mutableStateOf(employeeData.employeeName) }
    // 保留companyName变量以兼容现有接口，但在UI中不再显示
    var companyName by remember { mutableStateOf(employeeData.companyName.ifBlank { "自动解析" }) }

    var isValidUrl by remember { mutableStateOf(true) }
    val scrollState = rememberScrollState()

    // 服务器地址卡片是否展开
    var serverCardExpanded by remember { mutableStateOf(true) }
    // 基本信息卡片是否展开
    var infoCardExpanded by remember { mutableStateOf(true) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("服务器配置") }
            )
        }
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .padding(16.dp)
                .verticalScroll(scrollState),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 服务器配置部分 - 使用可折叠卡片
            ExpandableCard(
                title = "服务器地址设置",
                expanded = serverCardExpanded,
                onExpandChange = { serverCardExpanded = it },
                modifier = Modifier.fillMaxWidth()
            ) {
                // 服务器地址输入
                TextField(
                    value = serverUrl,
                    onValueChange = {
                        serverUrl = it
                        isValidUrl = validateUrl(it)
                    },
                    label = { Text("服务器地址") },
                    placeholder = { Text("例如: http://*************:3000") },
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Uri,
                        imeAction = ImeAction.Done
                    ),
                    isError = !isValidUrl,
                    supportingText = {
                        if (!isValidUrl) {
                            Text("请输入有效的URL格式 (http://ip:port)")
                        } else {
                            Text("此地址配置后很少需要修改")
                        }
                    },
                    singleLine = true
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 基本信息设置部分 - 使用可折叠卡片
            ExpandableCard(
                title = "基本信息设置",
                expanded = infoCardExpanded,
                onExpandChange = { infoCardExpanded = it },
                modifier = Modifier.fillMaxWidth()
            ) {
                // 员工姓名输入
                TextField(
                    value = employeeName,
                    onValueChange = { employeeName = it },
                    label = { Text("员工姓名") },
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Done
                    ),
                    singleLine = true,
                    supportingText = {
                        Text("请输入您的姓名")
                    }
                )

                // 注意：公司名称字段已移除，将从二维码中自动解析
            }

            Spacer(modifier = Modifier.height(32.dp))

            Button(
                onClick = {
                    if (isValidUrl) {
                        var finalUrl = serverUrl
                        // 确保URL以斜杠结尾
                        if (!finalUrl.endsWith("/")) {
                            finalUrl += "/"
                        }

                        // 保存所有配置数据
                        onSaveConfig(finalUrl, employeeName, companyName)
                    }
                },
                enabled = serverUrl.isNotBlank() && isValidUrl && employeeName.isNotBlank(),
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("保存配置")
            }

            if(employeeName.isBlank() || serverUrl.isBlank()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "请填写所有必要信息",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

// 验证URL格式
private fun validateUrl(url: String): Boolean {
    if (url.isBlank()) return true

    val urlRegex = Regex("^(http|https)://[\\w.-]+(:\\d+)?(/.*)?$")
    return urlRegex.matches(url)
}